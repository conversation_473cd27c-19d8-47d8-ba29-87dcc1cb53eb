@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}
:root {
  --radius: 0.75rem;

  --background: oklch(1 0 0); /* Light mode bg (white) */
  --foreground: oklch(0.1 0.02 140); /* Neon green contrast text */

  --card: oklch(0.98 0 0); /* Light cards */
  --card-foreground: oklch(0.1 0.02 140);

  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.1 0.02 140);

  --primary: oklch(0.75 0.26 130); /* Neon green */
  --primary-foreground: oklch(0.1 0.02 140); /* Text on neon */

  --secondary: oklch(0.9 0.05 260);
  --secondary-foreground: oklch(0.15 0.01 260);

  --muted: oklch(0.95 0.005 280);
  --muted-foreground: oklch(0.4 0.01 280);

  --accent: oklch(0.87 0.07 240);
  --accent-foreground: oklch(0.1 0.02 140);

  --destructive: oklch(0.63 0.22 30);
  --destructive-foreground: oklch(0.98 0.01 0);

  --border: oklch(0.9 0.005 280);
  --input: oklch(0.9 0.005 280);
  --ring: var(--primary);

  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);

  --sidebar: oklch(0.98 0 0);
  --sidebar-foreground: oklch(0.1 0.02 140);
  --sidebar-primary: var(--primary);
  --sidebar-primary-foreground: var(--primary-foreground);
  --sidebar-accent: var(--accent);
  --sidebar-accent-foreground: var(--accent-foreground);
  --sidebar-border: var(--border);
  --sidebar-ring: var(--ring);
}

.dark {
  --background: oklch(0.18 0.014 258.36); /* Deep navy */
  --foreground: oklch(0.985 0 0); /* Almost white text */

  --card: oklch(0.22 0.0157 256.82); /* Soft navy card */
  --card-foreground: oklch(0.985 0 0);

  --popover: oklch(0.22 0.0157 256.82);
  --popover-foreground: oklch(0.985 0 0);

  --primary: oklch(0.85 0.2156 132.08); /* Neon green */
  --primary-foreground: oklch(0.1 0.02 140); /* Text on neon */

  --secondary: oklch(0.35 0.015 260);
  --secondary-foreground: oklch(0.95 0 0);

  --muted: oklch(0.2 0.005 285.885);
  --muted-foreground: oklch(0.6 0.01 285.885);

  --accent: oklch(0.3 0.01 250);
  --accent-foreground: oklch(0.95 0 0);

  --destructive: oklch(0.704 0.191 22.216);
  --destructive-foreground: oklch(0.985 0 0);

  --border: oklch(0.26 0.0158 252.4);
  --input: oklch(1 0 0 / 15%);
  --ring: var(--primary);

  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);

  --sidebar: oklch(0.22 0.0157 256.82);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: var(--primary);
  --sidebar-primary-foreground: var(--primary-foreground);
  --sidebar-accent: var(--accent);
  --sidebar-accent-foreground: var(--accent-foreground);
  --sidebar-border: var(--border);
  --sidebar-ring: var(--ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
